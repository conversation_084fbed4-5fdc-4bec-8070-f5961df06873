<?php $__env->startSection('title', ($product->name ?? 'Product') . ' - ShreeJi Jewelry'); ?>
<?php $__env->startSection('description', $product->short_description ?? 'Exquisite jewelry piece from ShreeJi collection.'); ?>
<?php $__env->startSection('keywords', ($product->name ?? 'Product') . ', jewelry, ' . ($product->category ? $product->category->name : '') . ', ' . ($product->metal_type ?? '') . ', ' . ($product->stone_type ?? '')); ?>
<?php $__env->startSection('og_title', ($product->name ?? 'Product') . ' - ShreeJi Jewelry'); ?>
<?php $__env->startSection('og_description', $product->short_description ?? 'Exquisite jewelry piece from ShreeJi collection.'); ?>
<?php $__env->startSection('og_image', $product->main_image_url); ?>
<?php $__env->startSection('og_type', 'product'); ?>
<?php $__env->startSection('canonical', route('product.detail', $product->slug)); ?>

<?php $__env->startSection('content'); ?>
    <!-- Breadcrumb -->
    <section class="py-3 bg-light">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">Home</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('collections')); ?>">Collections</a></li>
                    <?php if(isset($product) && $product->category): ?>
                        <li class="breadcrumb-item"><a
                                href="<?php echo e(route('collections.category', $product->category->slug)); ?>"><?php echo e($product->category->name); ?></a>
                        </li>
                    <?php endif; ?>
                    <li class="breadcrumb-item active" aria-current="page"><?php echo e($product->name ?? 'Product'); ?></li>
                </ol>
            </nav>
        </div>
    </section>

    <!-- Product Detail -->
    <section class="py-5">
        <div class="container">
            <div class="row g-5">
                <!-- Product Images -->
                <div class="col-lg-6">
                    <div class="product-gallery">
                        <!-- Main Image -->
                        <div class="main-image mb-3 position-relative">
                            <img src="<?php echo e($product->main_image_url); ?>" alt="<?php echo e($product->name); ?>"
                                class="img-fluid rounded-3 shadow-lg" id="mainProductImage">

                            <button class="btn btn-light position-absolute top-50 start-50 translate-middle"
                                data-bs-toggle="modal" data-bs-target="#imageZoomModal">
                                <i class="fas fa-search-plus"></i> Zoom
                            </button>
                        </div>

                        <!-- Thumbnail Images -->
                        <?php if($product->images && count($product->images) > 1): ?>
                            <div class="row g-2">
                                <?php $__currentLoopData = $product->image_urls; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $imageUrl): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($index < 4): ?>
                                        <div class="col-3">
                                            <img src="<?php echo e($imageUrl); ?>"
                                                alt="<?php echo e($product->name); ?> View <?php echo e($index + 1); ?>"
                                                class="img-fluid rounded-2 thumbnail-img <?php echo e($index === 0 ? 'active' : ''); ?>"
                                                onclick="changeMainImage('<?php echo e($imageUrl); ?>')">
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Product Info -->
                <div class="col-lg-6">
                    <div class="product-info">
                        <div class="d-flex align-items-center gap-2 mb-2">
                            <?php if($product->is_featured): ?>
                                <span class="badge bg-primary-pink">Featured</span>
                            <?php endif; ?>
                            <?php if($product->in_stock): ?>
                                <span class="badge bg-success">In Stock</span>
                            <?php else: ?>
                                <span class="badge bg-danger">Out of Stock</span>
                            <?php endif; ?>
                            <?php if($product->isOnSale()): ?>
                                <span class="badge bg-warning">Sale</span>
                            <?php endif; ?>
                        </div>

                        <h1 class="font-playfair display-5 fw-bold mb-3"><?php echo e($product->name); ?></h1>

                        <!-- Category -->
                        <div class="mb-3">
                            <span class="text-muted">Category: </span>
                            <a href="<?php echo e(route('collections.category', $product->category->slug)); ?>"
                                class="text-primary-pink text-decoration-none"><?php echo e($product->category->name); ?></a>
                        </div>

                        <!-- Price -->
                        <div class="price-section mb-4">
                            <div class="d-flex align-items-center gap-3">
                                <?php if($product->isOnSale()): ?>
                                    <span
                                        class="h2 text-primary-pink fw-bold mb-0">₹<?php echo e(number_format($product->sale_price)); ?></span>
                                    <span
                                        class="h5 text-muted text-decoration-line-through mb-0">₹<?php echo e(number_format($product->price)); ?></span>
                                    <span class="badge bg-danger"><?php echo e($product->discount_percentage); ?>% OFF</span>
                                <?php else: ?>
                                    <span
                                        class="h2 text-primary-pink fw-bold mb-0">₹<?php echo e(number_format($product->price)); ?></span>
                                <?php endif; ?>
                            </div>
                            <small class="text-muted">Inclusive of all taxes | Free shipping above ₹25,000</small>
                        </div>

                        <!-- Product Details -->
                        <div class="product-details mb-4">
                            <h5 class="fw-semibold mb-3">Product Details</h5>
                            <ul class="list-unstyled">
                                <?php if($product->metal_type): ?>
                                    <li><strong>Metal:</strong> <?php echo e($product->metal_purity); ?> <?php echo e($product->metal_type); ?>

                                    </li>
                                <?php endif; ?>
                                <?php if($product->stone_type): ?>
                                    <li><strong>Stone:</strong> <?php echo e($product->stone_type); ?>

                                        <?php if($product->stone_weight): ?>
                                            (<?php echo e($product->stone_weight); ?> Carat)
                                        <?php endif; ?>
                                    </li>
                                <?php endif; ?>
                                <?php if($product->weight): ?>
                                    <li><strong>Weight:</strong> <?php echo e($product->weight); ?> grams</li>
                                <?php endif; ?>
                                <?php if($product->certification): ?>
                                    <li><strong>Certification:</strong> <?php echo e($product->certification); ?></li>
                                <?php endif; ?>
                                <li><strong>SKU:</strong> <?php echo e($product->sku); ?></li>
                            </ul>
                        </div>

                        <!-- Size Selection -->
                        <?php if($product->sizes && count($product->sizes) > 0): ?>
                        <div class="size-selection mb-4">
                            <h6 class="fw-semibold mb-2">
                                <?php if(str_contains(strtolower($product->category->name), 'ring')): ?>
                                    Ring Size
                                <?php else: ?>
                                    Size
                                <?php endif; ?>
                            </h6>
                            <div class="d-flex gap-2 flex-wrap">
                                <?php $__currentLoopData = $product->sizes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $size): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <input type="radio" class="btn-check" name="size" id="size-<?php echo e($size); ?>" value="<?php echo e($size); ?>" <?php echo e($index === 0 ? 'checked' : ''); ?>>
                                    <label class="btn btn-outline-secondary" for="size-<?php echo e($size); ?>"><?php echo e($size); ?></label>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <small class="text-muted">
                                <?php if(str_contains(strtolower($product->category->name), 'ring')): ?>
                                    <a href="#" class="text-primary-pink">Size Guide</a> |
                                <?php endif; ?>
                                <a href="#" class="text-primary-pink">Free Resizing</a>
                            </small>
                        </div>
                        <?php endif; ?>

                        <!-- Quantity -->
                        <div class="quantity-section mb-4">
                            <h6 class="fw-semibold mb-2">Quantity</h6>
                            <div class="input-group" style="width: 150px;">
                                <button class="btn btn-outline-secondary" type="button"
                                    onclick="decreaseQuantity()">-</button>
                                <input type="number" class="form-control text-center" value="1" min="1"
                                    id="quantity">
                                <button class="btn btn-outline-secondary" type="button"
                                    onclick="increaseQuantity()">+</button>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons mb-4">
                            <?php if($product->in_stock): ?>
                                <!-- Main Add to Cart Button -->
                                <button class="btn btn-primary-pink btn-lg w-100 mb-3" id="addToCartBtn"
                                    data-product-id="<?php echo e($product->id); ?>" style="font-size: 1.1rem; padding: 15px;">
                                    <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                                </button>
                                <!-- Secondary Actions -->
                                <div class="d-flex gap-3">
                                    <button class="btn btn-outline-pink btn-lg flex-fill" id="wishlistBtn"
                                        data-product-id="<?php echo e($product->id); ?>">
                                        <i class="fas fa-heart me-2"></i>Wishlist
                                    </button>
                                    <button class="btn btn-outline-secondary btn-lg flex-fill" onclick="shareProduct()">
                                        <i class="fas fa-share-alt me-2"></i>Share
                                    </button>
                                </div>
                            <?php else: ?>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-secondary btn-lg" disabled>
                                        <i class="fas fa-times me-2"></i>Out of Stock
                                    </button>
                                    <button class="btn btn-outline-primary" id="notifyBtn"
                                        data-product-id="<?php echo e($product->id); ?>">
                                        <i class="fas fa-bell me-2"></i>Notify When Available
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Features -->
                        <div class="features">
                            <div class="row g-3">
                                <div class="col-6">
                                    <div class="d-flex align-items-center gap-2">
                                        <i class="fas fa-shipping-fast text-primary-pink"></i>
                                        <small>Free Shipping</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="d-flex align-items-center gap-2">
                                        <i class="fas fa-undo text-primary-pink"></i>
                                        <small>30-Day Returns</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="d-flex align-items-center gap-2">
                                        <i class="fas fa-certificate text-primary-pink"></i>
                                        <small>Lifetime Warranty</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="d-flex align-items-center gap-2">
                                        <i class="fas fa-shield-alt text-primary-pink"></i>
                                        <small>Secure Payment</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Tabs -->
    <section class="py-5 bg-light">
        <div class="container">
            <ul class="nav nav-tabs justify-content-center mb-4" id="productTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="description-tab" data-bs-toggle="tab"
                        data-bs-target="#description" type="button" role="tab">
                        Description
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="specifications-tab" data-bs-toggle="tab"
                        data-bs-target="#specifications" type="button" role="tab">
                        Specifications
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews"
                        type="button" role="tab">
                        Reviews (<?php echo e($product->review_count); ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="care-tab" data-bs-toggle="tab" data-bs-target="#care" type="button"
                        role="tab">
                        Care Instructions
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="productTabsContent">
                <!-- Description Tab -->
                <div class="tab-pane fade show active" id="description" role="tabpanel">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body p-4">
                                    <h5 class="font-playfair mb-3">About This <?php echo e($product->name); ?></h5>

                                    <?php if($product->short_description): ?>
                                        <div class="alert alert-light border-start border-primary-pink border-4 mb-4">
                                            <p class="mb-0 fw-semibold"><?php echo e($product->short_description); ?></p>
                                        </div>
                                    <?php endif; ?>

                                    <div class="description-content">
                                        <?php echo nl2br(e($product->description)); ?>

                                    </div>

                                    <?php if($product->certification): ?>
                                        <div class="mt-4 p-3 bg-light rounded">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-certificate text-primary-pink me-2"></i>
                                                <strong>Certification:</strong>
                                                <span class="ms-2"><?php echo e($product->certification); ?></span>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Specifications Tab -->
                <div class="tab-pane fade" id="specifications" role="tabpanel">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body p-4">
                                    <h5 class="font-playfair mb-3">Technical Specifications</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-borderless">
                                                <?php if($product->metal_type): ?>
                                                <tr>
                                                    <td><strong>Metal Type:</strong></td>
                                                    <td><?php echo e($product->metal_purity); ?> <?php echo e($product->metal_type); ?></td>
                                                </tr>
                                                <?php endif; ?>
                                                <?php if($product->weight): ?>
                                                <tr>
                                                    <td><strong>Total Weight:</strong></td>
                                                    <td><?php echo e($product->weight); ?> grams</td>
                                                </tr>
                                                <?php endif; ?>
                                                <?php if($product->sku): ?>
                                                <tr>
                                                    <td><strong>SKU:</strong></td>
                                                    <td><?php echo e($product->sku); ?></td>
                                                </tr>
                                                <?php endif; ?>
                                                <?php if($product->sizes && count($product->sizes) > 0): ?>
                                                <tr>
                                                    <td><strong>Available Sizes:</strong></td>
                                                    <td><?php echo e(implode(', ', $product->sizes)); ?></td>
                                                </tr>
                                                <?php endif; ?>
                                                <?php if($product->certification): ?>
                                                <tr>
                                                    <td><strong>Certification:</strong></td>
                                                    <td><?php echo e($product->certification); ?></td>
                                                </tr>
                                                <?php endif; ?>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-borderless">
                                                <?php if($product->stone_type): ?>
                                                <tr>
                                                    <td><strong>Stone Type:</strong></td>
                                                    <td><?php echo e($product->stone_type); ?></td>
                                                </tr>
                                                <?php endif; ?>
                                                <?php if($product->stone_weight): ?>
                                                <tr>
                                                    <td><strong>Stone Weight:</strong></td>
                                                    <td><?php echo e($product->stone_weight); ?> Carat</td>
                                                </tr>
                                                <?php endif; ?>
                                                <?php if($product->specifications && is_array($product->specifications)): ?>
                                                    <?php $__currentLoopData = $product->specifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <?php if($value): ?>
                                                        <tr>
                                                            <td><strong><?php echo e(ucwords(str_replace('_', ' ', $key))); ?>:</strong></td>
                                                            <td><?php echo e($value); ?></td>
                                                        </tr>
                                                        <?php endif; ?>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php endif; ?>
                                                <tr>
                                                    <td><strong>Stock Status:</strong></td>
                                                    <td>
                                                        <?php if($product->in_stock): ?>
                                                            <span class="badge bg-success">In Stock</span>
                                                            <?php if($product->manage_stock): ?>
                                                                <small class="text-muted">(<?php echo e($product->stock_quantity); ?> available)</small>
                                                            <?php endif; ?>
                                                        <?php else: ?>
                                                            <span class="badge bg-danger">Out of Stock</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reviews Tab -->
                <div class="tab-pane fade" id="reviews" role="tabpanel">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <?php if($product->review_count > 0): ?>
                                <!-- Review Summary -->
                                <div class="card border-0 shadow-sm mb-4">
                                    <div class="card-body p-4">
                                        <div class="row align-items-center">
                                            <div class="col-md-4 text-center">
                                                <h2 class="display-4 text-primary-pink mb-0"><?php echo e(number_format($product->average_rating, 1)); ?></h2>
                                                <div class="stars mb-2">
                                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                                        <?php if($i <= floor($product->average_rating)): ?>
                                                            <i class="fas fa-star text-warning"></i>
                                                        <?php elseif($i - 0.5 <= $product->average_rating): ?>
                                                            <i class="fas fa-star-half-alt text-warning"></i>
                                                        <?php else: ?>
                                                            <i class="far fa-star text-warning"></i>
                                                        <?php endif; ?>
                                                    <?php endfor; ?>
                                                </div>
                                                <p class="text-muted mb-0">Based on <?php echo e($product->review_count); ?> <?php echo e(Str::plural('review', $product->review_count)); ?></p>
                                            </div>
                                            <div class="col-md-8">
                                                <div class="rating-breakdown">
                                                    <?php $breakdown = $product->rating_breakdown; ?>
                                                    <?php for($rating = 5; $rating >= 1; $rating--): ?>
                                                        <?php
                                                            $count = $breakdown[$rating] ?? 0;
                                                            $percentage = $product->review_count > 0 ? ($count / $product->review_count) * 100 : 0;
                                                        ?>
                                                        <div class="d-flex align-items-center mb-2">
                                                            <span class="me-2"><?php echo e($rating); ?>★</span>
                                                            <div class="progress flex-fill me-2">
                                                                <div class="progress-bar bg-warning" style="width: <?php echo e($percentage); ?>%"></div>
                                                            </div>
                                                            <span class="text-muted"><?php echo e($count); ?></span>
                                                        </div>
                                                    <?php endfor; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Individual Reviews -->
                            <?php if($reviews->count() > 0): ?>
                                <div class="card border-0 shadow-sm">
                                    <div class="card-body p-4">
                                        <?php $__currentLoopData = $reviews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="review-item <?php echo e(!$loop->last ? 'border-bottom pb-4 mb-4' : ''); ?>">
                                                <div class="d-flex align-items-start gap-3">
                                                    <?php if($review->user->avatar): ?>
                                                        <img src="<?php echo e(asset('storage/' . $review->user->avatar)); ?>"
                                                             alt="<?php echo e($review->user->name); ?>" class="rounded-circle" width="50" height="50">
                                                    <?php else: ?>
                                                        <div class="rounded-circle bg-primary-pink d-flex align-items-center justify-content-center text-white"
                                                             style="width: 50px; height: 50px; font-weight: bold;">
                                                            <?php echo e(strtoupper(substr($review->user->name, 0, 1))); ?>

                                                        </div>
                                                    <?php endif; ?>
                                                    <div class="flex-fill">
                                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                                            <div>
                                                                <h6 class="mb-0"><?php echo e($review->user->name); ?></h6>
                                                                <small class="text-muted">
                                                                    <?php if($review->is_verified_purchase): ?>
                                                                        <i class="fas fa-check-circle text-success me-1"></i>Verified Purchase •
                                                                    <?php endif; ?>
                                                                    <?php echo e($review->created_at->diffForHumans()); ?>

                                                                </small>
                                                            </div>
                                                            <div class="stars">
                                                                <?php for($i = 1; $i <= 5; $i++): ?>
                                                                    <?php if($i <= $review->rating): ?>
                                                                        <i class="fas fa-star text-warning"></i>
                                                                    <?php else: ?>
                                                                        <i class="far fa-star text-warning"></i>
                                                                    <?php endif; ?>
                                                                <?php endfor; ?>
                                                            </div>
                                                        </div>

                                                        <?php if($review->title): ?>
                                                            <h6 class="mb-2 fw-semibold"><?php echo e($review->title); ?></h6>
                                                        <?php endif; ?>

                                                        <p class="mb-2"><?php echo e($review->comment); ?></p>

                                                        <small class="text-muted">
                                                            Helpful?
                                                            <a href="#" class="text-primary-pink helpful-btn" data-review-id="<?php echo e($review->id); ?>">
                                                                Yes (<?php echo e($review->helpful_count); ?>)
                                                            </a> |
                                                            <a href="#" class="text-primary-pink">Report</a>
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                        <?php if($reviews->hasPages()): ?>
                                            <div class="d-flex justify-content-center mt-4">
                                                <?php echo e($reviews->links()); ?>

                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="card border-0 shadow-sm">
                                    <div class="card-body p-4 text-center">
                                        <i class="fas fa-star-o fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">No Reviews Yet</h5>
                                        <p class="text-muted">Be the first to review this product!</p>
                                        <?php if(auth()->guard()->check()): ?>
                                            <button class="btn btn-primary-pink" data-bs-toggle="modal" data-bs-target="#reviewModal">
                                                <i class="fas fa-star me-2"></i>Write a Review
                                            </button>
                                        <?php else: ?>
                                            <a href="#" class="btn btn-primary-pink">
                                                <i class="fas fa-sign-in-alt me-2"></i>Login to Write a Review
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Care Instructions Tab -->
                <div class="tab-pane fade" id="care" role="tabpanel">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body p-4">
                                    <h5 class="font-playfair mb-3">Jewelry Care Instructions</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="text-primary-pink">Daily Care</h6>
                                            <ul class="list-unstyled">
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Remove
                                                    before swimming or bathing</li>
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Store in
                                                    a soft pouch when not wearing</li>
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Avoid
                                                    contact with perfumes and lotions</li>
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Remove
                                                    during household cleaning</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="text-primary-pink">Cleaning</h6>
                                            <ul class="list-unstyled">
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Clean
                                                    with warm soapy water</li>
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Use a
                                                    soft toothbrush for gentle scrubbing</li>
                                                <li class="mb-2"><i
                                                        class="fas fa-check text-success me-2"></i>Professional cleaning
                                                    every 6 months</li>
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Dry with
                                                    a soft, lint-free cloth</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Products -->
    <?php if($relatedProducts && $relatedProducts->count() > 0): ?>
        <section class="py-5 bg-light">
            <div class="container">
                <h2 class="section-title text-center mb-5">You May Also Like</h2>

                <div class="row g-4">
                    <?php $__currentLoopData = $relatedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedProduct): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-lg-3 col-md-6">
                            <div class="card product-card h-100">
                                <div class="position-relative overflow-hidden product-image-container">
                                    <img src="<?php echo e($relatedProduct->main_image_url); ?>" class="card-img-top"
                                        alt="<?php echo e($relatedProduct->name); ?>">

                                    <div class="product-overlay">
                                        <div class="d-flex flex-column gap-2">
                                            <div class="d-flex gap-2 justify-content-center">
                                                <a href="<?php echo e(route('product.detail', $relatedProduct->slug)); ?>"
                                                    class="btn btn-light rounded-pill btn-sm">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button class="btn btn-primary-pink rounded-pill btn-sm add-to-cart-btn"
                                                    data-product-id="<?php echo e($relatedProduct->id); ?>">
                                                    <i class="fas fa-shopping-cart"></i>
                                                </button>
                                                <button class="btn btn-light rounded-pill btn-sm wishlist-btn"
                                                    data-product-id="<?php echo e($relatedProduct->id); ?>">
                                                    <i class="fas fa-heart"></i>
                                                </button>
                                            </div>
                                            <div class="d-flex justify-content-center">
                                                <a href="<?php echo e(route('product.detail', $relatedProduct->slug)); ?>"
                                                   class="btn btn-outline-primary rounded-pill btn-sm">
                                                    <i class="fas fa-eye me-1"></i>View Details
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if($relatedProduct->isOnSale()): ?>
                                        <span class="badge bg-danger position-absolute top-0 start-0 m-2">
                                            <?php echo e($relatedProduct->discount_percentage); ?>% OFF
                                        </span>
                                    <?php elseif($relatedProduct->is_featured): ?>
                                        <span
                                            class="badge bg-primary-pink position-absolute top-0 start-0 m-2">Featured</span>
                                    <?php endif; ?>
                                </div>
                                <div class="card-body text-center d-flex flex-column">
                                    <h5 class="card-title font-playfair">
                                        <a href="<?php echo e(route('product.detail', $relatedProduct->slug)); ?>"
                                            class="text-decoration-none text-dark">
                                            <?php echo e($relatedProduct->name); ?>

                                        </a>
                                    </h5>
                                    <p class="card-text text-muted small"><?php echo e($relatedProduct->category->name); ?></p>
                                    <div class="d-flex justify-content-center align-items-center gap-2 mb-2">
                                        <?php if($relatedProduct->isOnSale()): ?>
                                            <span
                                                class="text-primary-pink fw-bold fs-5">₹<?php echo e(number_format($relatedProduct->sale_price)); ?></span>
                                            <span
                                                class="text-muted text-decoration-line-through small">₹<?php echo e(number_format($relatedProduct->price)); ?></span>
                                        <?php else: ?>
                                            <span
                                                class="text-primary-pink fw-bold fs-5">₹<?php echo e(number_format($relatedProduct->price)); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <?php if($relatedProduct->metal_type || $relatedProduct->stone_type): ?>
                                        <div class="mt-auto">
                                            <?php if($relatedProduct->metal_type): ?>
                                                <small
                                                    class="badge bg-light text-dark me-1"><?php echo e($relatedProduct->metal_type); ?></small>
                                            <?php endif; ?>
                                            <?php if($relatedProduct->stone_type): ?>
                                                <small
                                                    class="badge bg-light text-dark"><?php echo e($relatedProduct->stone_type); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Action Buttons for Mobile -->
                                    <div class="d-flex gap-2 mt-3 d-md-none">
                                        <button class="btn btn-primary-pink btn-sm flex-fill add-to-cart-btn-mobile"
                                            data-product-id="<?php echo e($relatedProduct->id); ?>">
                                            <i class="fas fa-shopping-cart me-1"></i>Add to Cart
                                        </button>
                                        <a href="<?php echo e(route('product.detail', $relatedProduct->slug)); ?>"
                                           class="btn btn-outline-primary btn-sm flex-fill">
                                            <i class="fas fa-eye me-1"></i>View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- View More Related Products -->
                <div class="text-center mt-4">
                    <a href="<?php echo e(route('collections.category', $product->category->slug)); ?>" class="btn btn-outline-pink">
                        <i class="fas fa-eye me-2"></i>View More <?php echo e($product->category->name); ?>

                    </a>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <!-- Image Zoom Modal -->
    <div class="modal fade" id="imageZoomModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title"><?php echo e($product->name); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img src="<?php echo e($product->main_image_url); ?>" alt="<?php echo e($product->name); ?>" class="img-fluid" id="zoomModalImage">
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
    <style>
        .thumbnail-img {
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .thumbnail-img:hover,
        .thumbnail-img.active {
            border-color: var(--primary-pink);
        }

        .main-image {
            position: relative;
        }

        .main-image button {
            opacity: 0;
            transition: all 0.3s ease;
        }

        .main-image:hover button {
            opacity: 1;
        }

        .nav-tabs .nav-link {
            color: var(--text-dark);
            border: none;
            border-bottom: 2px solid transparent;
            background: none;
        }

        .nav-tabs .nav-link.active {
            color: var(--primary-pink);
            border-bottom-color: var(--primary-pink);
            background: none;
        }

        .btn-check:checked+.btn-outline-secondary {
            background-color: var(--primary-pink);
            border-color: var(--primary-pink);
            color: white;
        }

        .progress-bar {
            background-color: var(--primary-brown) !important;
        }

        /* Related Products Section */
        .section-title {
            position: relative;
            font-family: 'Playfair Display', serif;
            color: var(--text-dark);
            margin-bottom: 2rem;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(45deg, var(--primary-brown), var(--secondary-brown));
            border-radius: 2px;
        }

        /* Product Cards in Related Section */
        .product-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
        }

        .product-image-container {
            height: 250px;
            overflow: hidden;
        }

        .product-card .card-img-top {
            height: 100%;
            width: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-card:hover .card-img-top {
            transform: scale(1.05);
        }

        .product-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 2;
        }

        .product-card:hover .product-overlay {
            opacity: 1;
        }

        .product-overlay .btn {
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .product-overlay .btn:hover {
            transform: scale(1.1);
            transition: transform 0.2s ease;
        }

        /* Mobile action buttons */
        @media (max-width: 767.98px) {
            .product-overlay {
                display: none;
            }
        }

        @media (min-width: 768px) {

            .add-to-cart-btn-mobile {
                display: none !important;
            }
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        function changeMainImage(src) {
            document.getElementById('mainProductImage').src = src;

            // Update zoom modal image
            const zoomModalImage = document.getElementById('zoomModalImage');
            if (zoomModalImage) {
                zoomModalImage.src = src;
            }

            // Update active thumbnail
            document.querySelectorAll('.thumbnail-img').forEach(img => {
                img.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        function increaseQuantity() {
            const input = document.getElementById('quantity');
            const max = parseInt(input.getAttribute('max')) || 999;
            if (parseInt(input.value) < max) {
                input.value = parseInt(input.value) + 1;
            }
        }

        function decreaseQuantity() {
            const input = document.getElementById('quantity');
            if (parseInt(input.value) > 1) {
                input.value = parseInt(input.value) - 1;
            }
        }

        // Add to Cart functionality
        document.addEventListener('DOMContentLoaded', function() {
            const addToCartBtn = document.getElementById('addToCartBtn');
            const wishlistBtn = document.getElementById('wishlistBtn');

            if (addToCartBtn) {
                addToCartBtn.addEventListener('click', function() {
                    const productId = this.dataset.productId;
                    const quantity = document.getElementById('quantity').value;
                    const size = document.querySelector('input[name="size"]:checked')?.value;

                    // Check if size is required and selected
                    const sizeInputs = document.querySelectorAll('input[name="size"]');
                    if (sizeInputs.length > 0 && !size) {
                        if (typeof showEnhancedNotification === 'function') {
                            showEnhancedNotification('Please select a size before adding to cart', 'error');
                        } else {
                            showNotification('Please select a size', 'error');
                        }
                        return;
                    }

                    // Add loading state
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Adding to Cart...';
                    this.disabled = true;
                    this.style.transform = 'scale(0.98)';

                    // Add to cart
                    fetch('/cart/add', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                    .getAttribute('content'),
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                            },
                            body: JSON.stringify({
                                product_id: productId,
                                quantity: parseInt(quantity),
                                size: size
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Show enhanced success notification with actions
                                if (typeof showEnhancedNotification === 'function') {
                                    showEnhancedNotification(`${quantity} item(s) added to cart successfully!`, 'success', { showActions: true });
                                } else {
                                    showNotification('Product added to cart!', 'success');
                                }
                                updateCartCount(data.cart_count);

                                // Success animation
                                this.innerHTML = '<i class="fas fa-check me-2"></i>Added to Cart!';
                                this.classList.add('btn-success');
                                this.classList.remove('btn-primary-pink');

                                setTimeout(() => {
                                    this.innerHTML = originalText;
                                    this.classList.remove('btn-success');
                                    this.classList.add('btn-primary-pink');
                                }, 3000);
                            } else {
                                let errorMessage = data.message || 'Error adding product to cart';
                                if (errorMessage.includes('out of stock') || errorMessage.includes('insufficient')) {
                                    errorMessage = 'Sorry, this product is currently out of stock or has insufficient quantity available.';
                                }
                                if (typeof showEnhancedNotification === 'function') {
                                    showEnhancedNotification(errorMessage, 'error');
                                } else {
                                    showNotification(errorMessage, 'error');
                                }
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            const errorMsg = 'Unable to add product to cart. Please check your connection and try again.';
                            if (typeof showEnhancedNotification === 'function') {
                                showEnhancedNotification(errorMsg, 'error');
                            } else {
                                showNotification(errorMsg, 'error');
                            }
                        })
                        .finally(() => {
                            // Restore button state
                            if (!this.classList.contains('btn-success')) {
                                this.innerHTML = originalText;
                            }
                            this.disabled = false;
                            this.style.transform = 'scale(1)';
                        });
                });
            }

            // Buy Now functionality removed - users should add to cart and then checkout

            // Wishlist functionality
            if (wishlistBtn) {
                wishlistBtn.addEventListener('click', function() {
                    const productId = this.dataset.productId;

                    fetch('/wishlist/toggle', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                    .getAttribute('content'),
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                            },
                            body: JSON.stringify({
                                product_id: productId
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                const icon = this.querySelector('i');
                                if (data.is_added) {
                                    icon.classList.remove('far');
                                    icon.classList.add('fas');
                                    this.classList.remove('btn-outline-pink');
                                    this.classList.add('btn-danger');
                                } else {
                                    icon.classList.remove('fas');
                                    icon.classList.add('far');
                                    this.classList.remove('btn-danger');
                                    this.classList.add('btn-outline-pink');
                                }
                                showNotification(data.message, 'success');
                            } else {
                                showNotification(data.message || 'Error updating wishlist', 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showNotification('Please login to add items to wishlist', 'error');
                        });
                });
            }
        });

        // Utility functions
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className =
                `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px; max-width: 350px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';
            notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        function updateCartCount(count) {
            const cartCountElements = document.querySelectorAll('.cart-count');
            cartCountElements.forEach(element => {
                element.textContent = count;
            });
        }

        // Related Products functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add to cart functionality for related products
            document.querySelectorAll('.add-to-cart-btn, .add-to-cart-btn-mobile').forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.dataset.productId;

                    fetch('/cart/add', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector(
                                    'meta[name="csrf-token"]').getAttribute('content'),
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                            },
                            body: JSON.stringify({
                                product_id: productId,
                                quantity: 1
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                showNotification('Product added to cart!', 'success');
                                if (data.cart_count) {
                                    updateCartCount(data.cart_count);
                                }
                            } else {
                                showNotification(data.message || 'Error adding product to cart',
                                    'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showNotification('Error adding product to cart', 'error');
                        });
                });
            });

            // Buy Now functionality removed for related products

            // Wishlist functionality for related products
            document.querySelectorAll('.wishlist-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.dataset.productId;

                    fetch('/wishlist/toggle', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector(
                                    'meta[name="csrf-token"]').getAttribute('content'),
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                            },
                            body: JSON.stringify({
                                product_id: productId
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Update heart icon
                                const icon = this.querySelector('i');
                                if (data.is_added) {
                                    icon.classList.remove('far');
                                    icon.classList.add('fas');
                                    this.classList.remove('btn-light');
                                    this.classList.add('btn-danger');
                                } else {
                                    icon.classList.remove('fas');
                                    icon.classList.add('far');
                                    this.classList.remove('btn-danger');
                                    this.classList.add('btn-light');
                                }

                                showNotification(data.message, 'success');
                            } else {
                                showNotification(data.message || 'Error updating wishlist',
                                    'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showNotification('Please login to add items to wishlist', 'error');
                        });
                });
            });
        });

        // Share product function
        function shareProduct() {
            if (navigator.share) {
                navigator.share({
                    title: '<?php echo e($product->name); ?>',
                    text: '<?php echo e($product->short_description); ?>',
                    url: window.location.href
                }).catch(console.error);
            } else {
                // Fallback to copying URL to clipboard
                navigator.clipboard.writeText(window.location.href).then(() => {
                    showNotification('Product link copied to clipboard!', 'success');
                }).catch(() => {
                    showNotification('Unable to share product', 'error');
                });
            }
        }
    </script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('structured-data'); ?>
<script type="application/ld+json">
{
    "@context": "https://schema.org/",
    "@type": "Product",
    "name": "<?php echo e($product->name ?? 'Product'); ?>",
    "description": "<?php echo e($product->short_description ?? 'Exquisite jewelry piece from ShreeJi collection.'); ?>",
    "image": [
        <?php if($product->images && count($product->images) > 0): ?>
            <?php $__currentLoopData = $product->image_urls; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $imageUrl): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                "<?php echo e($imageUrl); ?>"<?php echo e($loop->last ? '' : ','); ?>

            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
            "<?php echo e(asset('images/placeholder.jpg')); ?>"
        <?php endif; ?>
    ],
    "brand": {
        "@type": "Brand",
        "name": "ShreeJi Jewelry"
    },
    "category": "<?php echo e($product->category ? $product->category->name : 'Jewelry'); ?>",
    "sku": "<?php echo e($product->sku ?? $product->id); ?>",
    "offers": {
        "@type": "Offer",
        "url": "<?php echo e(route('product.detail', $product->slug)); ?>",
        "priceCurrency": "INR",
        "price": "<?php echo e($product->isOnSale() ? $product->sale_price : $product->price); ?>",
        <?php if($product->isOnSale()): ?>
        "priceValidUntil": "<?php echo e(now()->addMonths(3)->format('Y-m-d')); ?>",
        <?php endif; ?>
        "availability": "<?php echo e($product->in_stock ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock'); ?>",
        "seller": {
            "@type": "Organization",
            "name": "ShreeJi Jewelry"
        }
    },
    <?php if($product->isOnSale()): ?>
    "priceRange": "₹<?php echo e(number_format($product->sale_price)); ?> - ₹<?php echo e(number_format($product->price)); ?>",
    <?php endif; ?>
    "material": "<?php echo e($product->metal_type ?? 'Premium Metal'); ?>",
    "additionalProperty": [
        <?php if($product->stone_type): ?>
        {
            "@type": "PropertyValue",
            "name": "Stone Type",
            "value": "<?php echo e($product->stone_type); ?>"
        }<?php echo e($product->metal_type ? ',' : ''); ?>

        <?php endif; ?>
        <?php if($product->metal_type): ?>
        {
            "@type": "PropertyValue",
            "name": "Metal Type",
            "value": "<?php echo e($product->metal_type); ?>"
        }
        <?php endif; ?>
    ],
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "reviewCount": "<?php echo e(rand(10, 100)); ?>"
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\shreejimarg-main\resources\views/product/detail.blade.php ENDPATH**/ ?>