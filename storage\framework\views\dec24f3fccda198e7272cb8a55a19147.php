<?php $__env->startSection('title', 'Shopping Cart - ShreeJi Jewelry'); ?>
<?php $__env->startSection('description', 'Review your selected jewelry items and proceed to checkout.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Breadcrumb -->
<section class="py-3 bg-light">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">Shopping Cart</li>
            </ol>
        </nav>
    </div>
</section>

<!-- Shopping Cart -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="font-playfair display-5 fw-bold mb-4">Shopping Cart</h1>
            </div>
        </div>
        
        <div class="row g-5">
            <!-- Cart Items -->
            <div class="col-lg-8">
                <div class="cart-items">
                    <?php if($cartItems && $cartItems->count() > 0): ?>
                        <?php $__currentLoopData = $cartItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="card mb-4 border-0 shadow-sm" id="cart-item-<?php echo e($item->id); ?>">
                            <div class="card-body p-4">
                                <div class="row align-items-center">
                                    <div class="col-md-2">
                                        <img src="<?php echo e($item->product->main_image_url); ?>"
                                             alt="<?php echo e($item->product->name); ?>" class="img-fluid rounded">
                                    </div>
                                    <div class="col-md-4">
                                        <h5 class="font-playfair mb-1">
                                            <a href="<?php echo e(route('product.detail', $item->product->slug)); ?>" class="text-decoration-none text-dark">
                                                <?php echo e($item->product->name); ?>

                                            </a>
                                        </h5>
                                        <p class="text-muted mb-1"><?php echo e($item->product->category->name); ?></p>
                                        <small class="text-muted">
                                            <?php if($item->size): ?>
                                                Size: <?php echo e($item->size); ?> |
                                            <?php endif; ?>
                                            SKU: <?php echo e($item->product->sku); ?>

                                        </small>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="input-group input-group-sm">
                                            <button class="btn btn-outline-secondary" type="button" onclick="updateQuantity(<?php echo e($item->id); ?>, -1)">-</button>
                                            <input type="number" class="form-control text-center" value="<?php echo e($item->quantity); ?>" min="1" id="qty-<?php echo e($item->id); ?>" readonly>
                                            <button class="btn btn-outline-secondary" type="button" onclick="updateQuantity(<?php echo e($item->id); ?>, 1)">+</button>
                                        </div>
                                    </div>
                                    <div class="col-md-2 text-center">
                                        <?php if($item->product->isOnSale()): ?>
                                            <span class="h5 text-primary-pink fw-bold">₹<?php echo e(number_format($item->product->sale_price * $item->quantity)); ?></span>
                                            <br><small class="text-muted text-decoration-line-through">₹<?php echo e(number_format($item->product->price * $item->quantity)); ?></small>
                                        <?php else: ?>
                                            <span class="h5 text-primary-pink fw-bold">₹<?php echo e(number_format($item->product->price * $item->quantity)); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-2 text-end">
                                        <button class="btn btn-outline-danger btn-sm" onclick="removeItem(<?php echo e($item->id); ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm ms-1" onclick="moveToWishlist(<?php echo e($item->id); ?>)">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                        <!-- Empty Cart -->
                        <div class="text-center py-5">
                            <i class="fas fa-shopping-cart text-muted mb-4" style="font-size: 4rem;"></i>
                            <h3 class="font-playfair mb-3">Your Cart is Empty</h3>
                            <p class="text-muted mb-4">Looks like you haven't added any items to your cart yet.</p>
                            <a href="<?php echo e(route('collections')); ?>" class="btn btn-primary-pink btn-lg">
                                <i class="fas fa-gem me-2"></i>Start Shopping
                            </a>
                        </div>
                    <?php endif; ?>

                    <?php if($cartItems && $cartItems->count() > 0): ?>
                    <!-- Continue Shopping -->
                    <div class="text-center mt-4">
                        <a href="<?php echo e(route('collections')); ?>" class="btn btn-outline-pink">
                            <i class="fas fa-arrow-left me-2"></i>Continue Shopping
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Order Summary -->
            <div class="col-lg-4">
                <?php if($cartItems && $cartItems->count() > 0): ?>
                <div class="card border-0 shadow-sm sticky-top" style="top: 100px;">
                    <div class="card-header bg-gradient-pink text-white">
                        <h5 class="font-playfair mb-0">Order Summary</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal (<?php echo e($cartItems->sum('quantity')); ?> items)</span>
                            <span id="subtotal">₹<?php echo e(number_format($subtotal)); ?></span>
                        </div>
                        <?php if(session('applied_promocode')): ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Promo (<?php echo e(session('applied_promocode.code')); ?>)</span>
                            <span class="text-success" id="promoDiscount">-₹<?php echo e(number_format(session('applied_promocode.discount'))); ?></span>
                        </div>
                        <?php else: ?>
                        <div class="d-flex justify-content-between mb-2" id="promoDiscountRow" style="display: none;">
                            <span>Promo (<span id="promoCodeName"></span>)</span>
                            <span class="text-success" id="promoDiscount">-₹0</span>
                        </div>
                        <?php endif; ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Shipping</span>
                            <?php if($subtotal >= 25000): ?>
                                <span class="text-success">Free</span>
                            <?php else: ?>
                                <span>₹500</span>
                            <?php endif; ?>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax (GST 3%)</span>
                            <span id="tax">₹<?php echo e(number_format($tax)); ?></span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>Total</strong>
                            <strong class="text-primary-pink h5" id="total">₹<?php echo e(number_format($total)); ?></strong>
                        </div>

                        <!-- Promo Code -->
                        <div class="mb-3">
                            <?php if(session('applied_promocode')): ?>
                            <div class="alert alert-success d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-tag me-2"></i>
                                    <strong><?php echo e(session('applied_promocode.code')); ?></strong> applied!
                                    <br><small>You saved ₹<?php echo e(number_format(session('applied_promocode.discount'))); ?></small>
                                </div>
                                <button class="btn btn-sm btn-outline-danger" onclick="removePromoCode()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <?php else: ?>
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Enter promo code" id="promoCode">
                                <button class="btn btn-outline-secondary" type="button" onclick="applyPromoCode()">Apply</button>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Checkout Button -->
                        <a href="<?php echo e(route('checkout')); ?>" class="btn btn-primary-pink w-100 btn-lg mb-3">
                            <i class="fas fa-lock me-2"></i>Secure Checkout
                        </a>

                        <!-- Payment Methods -->
                        <div class="text-center">
                            <small class="text-muted d-block mb-2">We Accept</small>
                            <div class="d-flex justify-content-center gap-2">
                                <i class="fab fa-cc-visa fs-4 text-primary"></i>
                                <i class="fab fa-cc-mastercard fs-4 text-warning"></i>
                                <i class="fab fa-cc-amex fs-4 text-info"></i>
                                <i class="fab fa-paypal fs-4 text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Security Features -->
                <div class="card border-0 shadow-sm mt-4">
                    <div class="card-body p-4">
                        <h6 class="font-playfair mb-3">Why Shop With Us?</h6>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-shield-alt text-primary-pink me-2"></i>
                            <small>Secure SSL Encryption</small>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-undo text-primary-pink me-2"></i>
                            <small>30-Day Easy Returns</small>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-shipping-fast text-primary-pink me-2"></i>
                            <small>Free Shipping Above ₹25,000</small>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-certificate text-primary-pink me-2"></i>
                            <small>Lifetime Warranty</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Recently Viewed -->
<section class="py-5 bg-light">
    <div class="container">
        <h3 class="font-playfair mb-4">Recently Viewed</h3>
        <?php if($recentlyViewedProducts && $recentlyViewedProducts->count() > 0): ?>
            <div class="row g-4">
                <?php $__currentLoopData = $recentlyViewedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-3 col-md-6">
                        <div class="card product-card h-100">
                            <div class="position-relative overflow-hidden">
                                <img src="<?php echo e($product->main_image_url); ?>"
                                     class="card-img-top" alt="<?php echo e($product->name); ?>"
                                     style="height: 200px; object-fit: cover;"
                                     onerror="this.src='https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'">
                                <div class="product-overlay">
                                    <a href="<?php echo e(route('product.detail', $product->slug)); ?>" class="btn btn-light rounded-pill">
                                        <i class="fas fa-eye me-2"></i>View Details
                                    </a>
                                </div>
                            </div>
                            <div class="card-body text-center">
                                <h5 class="card-title font-playfair"><?php echo e($product->name); ?></h5>
                                <p class="card-text text-muted"><?php echo e($product->category->name ?? 'Jewelry'); ?></p>
                                <div class="d-flex justify-content-center align-items-center mb-3">
                                    <?php if($product->isOnSale()): ?>
                                        <span class="text-muted text-decoration-line-through me-2">₹<?php echo e(number_format($product->price)); ?></span>
                                        <span class="text-primary-pink fw-bold">₹<?php echo e(number_format($product->sale_price)); ?></span>
                                    <?php else: ?>
                                        <span class="text-primary-pink fw-bold">₹<?php echo e(number_format($product->price)); ?></span>
                                    <?php endif; ?>
                                </div>
                                <?php if($product->in_stock): ?>
                                    <button class="btn btn-primary-pink btn-sm add-to-cart-btn" data-product-id="<?php echo e($product->id); ?>">
                                        <i class="fas fa-shopping-cart me-1"></i>Add to Cart
                                    </button>
                                <?php else: ?>
                                    <button class="btn btn-secondary btn-sm" disabled>
                                        <i class="fas fa-times me-1"></i>Out of Stock
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-eye text-muted mb-3" style="font-size: 3rem;"></i>
                <h5 class="text-muted">No Recently Viewed Products</h5>
                <p class="text-muted">Products you view will appear here for easy access.</p>
                <a href="<?php echo e(route('collections')); ?>" class="btn btn-primary-pink">
                    <i class="fas fa-gem me-2"></i>Browse Products
                </a>
            </div>
        <?php endif; ?>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    function updateQuantity(itemId, change) {
        const qtyInput = document.getElementById(`qty-${itemId}`);
        const currentQty = parseInt(qtyInput.value);
        const newQty = currentQty + change;

        if (newQty >= 1) {
            // Send AJAX request to update quantity
            fetch('/cart/update', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    item_id: itemId,
                    quantity: newQty
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    qtyInput.value = newQty;
                    updateCartDisplay(data);
                    showNotification('Cart updated successfully', 'success');
                } else {
                    showNotification(data.message || 'Error updating cart', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error updating cart', 'error');
            });
        }
    }

    function removeItem(itemId) {
        if (confirm('Are you sure you want to remove this item from your cart?')) {
            fetch('/cart/remove', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    item_id: itemId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById(`cart-item-${itemId}`).remove();
                    updateCartDisplay(data);
                    updateCartCount(data.cart_count);
                    showNotification('Item removed from cart', 'success');

                    // If cart is empty, reload page to show empty state
                    if (data.cart_count === 0) {
                        location.reload();
                    }
                } else {
                    showNotification(data.message || 'Error removing item', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error removing item', 'error');
            });
        }
    }

    function moveToWishlist(itemId) {
        if (confirm('Move this item to your wishlist?')) {
            fetch('/cart/move-to-wishlist', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    item_id: itemId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById(`cart-item-${itemId}`).remove();
                    updateCartDisplay(data);
                    updateCartCount(data.cart_count);
                    showNotification('Item moved to wishlist', 'success');

                    // If cart is empty, reload page to show empty state
                    if (data.cart_count === 0) {
                        location.reload();
                    }
                } else {
                    showNotification(data.message || 'Please login to use wishlist', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Please login to use wishlist', 'error');
            });
        }
    }

    function applyPromoCode() {
        const promoCode = document.getElementById('promoCode').value.trim();

        if (!promoCode) {
            showNotification('Please enter a promo code', 'error');
            return;
        }

        fetch('/cart/apply-promo', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
            body: JSON.stringify({
                promo_code: promoCode
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateCartDisplayWithPromo(data);
                showNotification('Promo code applied successfully!', 'success');
                // Reload page to show applied promocode UI
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Invalid promo code', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error applying promo code', 'error');
        });
    }

    function updateCartDisplay(data) {
        // Update totals
        if (data.subtotal !== undefined) {
            document.getElementById('subtotal').textContent = '₹' + data.subtotal.toLocaleString();
        }
        if (data.tax !== undefined) {
            document.getElementById('tax').textContent = '₹' + data.tax.toLocaleString();
        }
        if (data.total !== undefined) {
            document.getElementById('total').textContent = '₹' + data.total.toLocaleString();
        }
    }

    function updateCartCount(count) {
        const cartCountElements = document.querySelectorAll('.cart-count');
        cartCountElements.forEach(element => {
            element.textContent = count;
        });
    }

    function showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px; max-width: 350px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    function removePromoCode() {
        fetch('/cart/remove-promo', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Promo code removed', 'success');
                // Reload page to update UI
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Error removing promo code', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error removing promo code', 'error');
        });
    }

    function updateCartDisplayWithPromo(data) {
        // Update subtotal
        document.getElementById('subtotal').textContent = '₹' + new Intl.NumberFormat('en-IN').format(data.subtotal);

        // Discount feature removed

        // Update or show promo discount
        const promoRow = document.getElementById('promoDiscountRow');
        const promoDiscount = document.getElementById('promoDiscount');
        const promoCodeName = document.getElementById('promoCodeName');

        if (data.promo_discount > 0) {
            promoRow.style.display = 'flex';
            promoDiscount.textContent = '-₹' + new Intl.NumberFormat('en-IN').format(data.promo_discount);
            promoCodeName.textContent = data.promocode.code;
        } else {
            promoRow.style.display = 'none';
        }

        // Update shipping
        document.getElementById('shipping').textContent = data.shipping > 0 ? '₹' + new Intl.NumberFormat('en-IN').format(data.shipping) : 'Free';

        // Update tax
        document.getElementById('tax').textContent = '₹' + new Intl.NumberFormat('en-IN').format(data.tax);

        // Update total
        document.getElementById('total').textContent = '₹' + new Intl.NumberFormat('en-IN').format(data.total);
    }

    // Add to cart functionality for recently viewed products
    document.querySelectorAll('.add-to-cart-btn').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.dataset.productId;
            const originalText = this.innerHTML;

            // Add loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Adding...';
            this.disabled = true;

            fetch('/cart/add', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: 1
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show enhanced success notification if available
                    if (typeof showEnhancedNotification === 'function') {
                        showEnhancedNotification('Product added to cart successfully!', 'success', { showActions: true });
                    } else {
                        showNotification('Product added to cart!', 'success');
                    }

                    // Update cart count
                    if (data.cart_count) {
                        updateCartCount(data.cart_count);
                    }

                    // Success animation
                    this.innerHTML = '<i class="fas fa-check me-1"></i>Added!';
                    this.classList.remove('btn-primary-pink');
                    this.classList.add('btn-success');

                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.classList.remove('btn-success');
                        this.classList.add('btn-primary-pink');
                        this.disabled = false;
                    }, 2000);
                } else {
                    let errorMessage = data.message || 'Error adding product to cart';
                    if (typeof showEnhancedNotification === 'function') {
                        showEnhancedNotification(errorMessage, 'error');
                    } else {
                        showNotification(errorMessage, 'error');
                    }

                    // Restore button
                    this.innerHTML = originalText;
                    this.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                const errorMsg = 'Unable to add product to cart. Please try again.';
                if (typeof showEnhancedNotification === 'function') {
                    showEnhancedNotification(errorMsg, 'error');
                } else {
                    showNotification(errorMsg, 'error');
                }

                // Restore button
                this.innerHTML = originalText;
                this.disabled = false;
            });
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\shreejimarg-main\resources\views/cart/index.blade.php ENDPATH**/ ?>