<?php $__env->startSection('title', $metaTags['title'] ?? $page->title); ?>
<?php $__env->startSection('description', $metaTags['description'] ?? ''); ?>

<?php if(isset($metaTags['keywords'])): ?>
<?php $__env->startSection('keywords', $metaTags['keywords']); ?>
<?php endif; ?>

<?php $__env->startPush('meta'); ?>
<?php echo App\Helpers\SeoHelper::renderMetaTags($metaTags); ?>

<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Compact About Hero Section -->
<section class="py-4 bg-gradient-to-r from-pink-50 to-purple-50" style="background: linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 100%);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="font-playfair display-4 fw-bold text-dark mb-3"><?php echo e($page->title); ?></h1>
                <?php if($page->excerpt): ?>
                <p class="lead text-muted mb-0"><?php echo e($page->excerpt); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Page Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-10 mx-auto">
                <?php if($page->featured_image): ?>
                <div class="text-center mb-5">
                    <img src="<?php echo e($page->featured_image); ?>"
                         alt="<?php echo e($page->title); ?>"
                         class="img-fluid rounded shadow">
                </div>
                <?php endif; ?>

                <div class="content">
                    <?php echo $page->content; ?>

                </div>

                <?php if($page->updated_at): ?>
                <div class="mt-5 pt-4 border-top text-muted small">
                    <div class="row">
                        <div class="col-md-6">
                            <i class="fas fa-calendar me-2"></i>
                            Last updated: <?php echo e($page->updated_at->format('F d, Y')); ?>

                        </div>
                        <?php if($page->updater): ?>
                        <div class="col-md-6 text-md-end">
                            <i class="fas fa-user me-2"></i>
                            Updated by: <?php echo e($page->updater->name); ?>

                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section for About Page -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h3 class="font-playfair mb-4">Explore Our Collections</h3>
                <p class="text-muted mb-4">
                    Discover our exquisite range of jewelry crafted with love and precision.
                </p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="<?php echo e(route('collections')); ?>" class="btn btn-primary-pink">
                        <i class="fas fa-gem me-2"></i>View Collections
                    </a>
                    <a href="<?php echo e(route('products')); ?>" class="btn btn-outline-primary-pink">
                        <i class="fas fa-shopping-bag me-2"></i>Shop Now
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Structured Data -->
<?php $__env->startPush('structured-data'); ?>
<?php echo App\Helpers\SeoHelper::renderStructuredData($structuredData); ?>

<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.content h2 {
    font-family: 'Playfair Display', serif;
    color: #2c3e50;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.content h3 {
    font-family: 'Playfair Display', serif;
    color: #34495e;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}

.content p {
    margin-bottom: 1.25rem;
}

.content ul, .content ol {
    margin-bottom: 1.25rem;
    padding-left: 2rem;
}

.content li {
    margin-bottom: 0.5rem;
}

.content blockquote {
    border-left: 4px solid var(--bs-primary);
    padding-left: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: #6c757d;
}

.content img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin: 1rem 0;
}

.content table {
    width: 100%;
    margin: 1.5rem 0;
    border-collapse: collapse;
}

.content table th,
.content table td {
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    text-align: left;
}

.content table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.btn-primary-pink {
    background-color: #db7093;
    border-color: #db7093;
}

.btn-primary-pink:hover {
    background-color: #c85a7a;
    border-color: #c85a7a;
}

.btn-outline-primary-pink {
    color: #db7093;
    border-color: #db7093;
}

.btn-outline-primary-pink:hover {
    background-color: #db7093;
    border-color: #db7093;
    color: white;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\shreejimarg-main\resources\views/pages/templates/about.blade.php ENDPATH**/ ?>